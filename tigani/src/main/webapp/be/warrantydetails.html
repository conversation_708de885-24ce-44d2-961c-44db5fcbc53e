{% extends "be/include/base.html" %}

{% set page = 'WARRANTYDETAILS' %}
{% set title = curWarrantyDetails is empty ? 'Nuovo Dettaglio Garanzia' : 'Modifica Dettaglio Garanzia' %}

{% block extrahead %}

<title>{{ title }} </title>

<!-- Specific script -->
{% include "be/include/snippets/plugins/validate.html" %}
{% include "be/include/snippets/plugins/maxlength.html" %}
{% include "be/include/snippets/plugins/select2.html" %}
<!-- specific script-->

<!-- Page script -->
<script src="{{ contextPath }}/be/js/pages/warrantydetails.js?{{ buildNumber }}"></script>
<!-- /page script -->

{% endblock %}

{% block content %}
<script class="reload-script-on-load">
    addRoute('BE_WARRANTYDETAILS', '{{ routes("BE_WARRANTYDETAILS") }}');
    addRoute('BE_WARRANTY_CRITERIA', '{{ routes("BE_WARRANTY_CRITERIA") }}');

    // Pass warranty ID parameter if present
    {% if request.queryParams("warrantyId") is not empty %}
    window.preselectedWarrantyId = '{{ request.queryParams("warrantyId") }}';
    {% endif %}
</script>

<!-- Content area -->
<div class="content container pt-0">

    <!-- Form -->
    <div class="card">
        <div class="card-header">
            {% if curWarrantyDetails is empty %}
            <h5 class="mb-0">Inserisci Dettaglio Garanzia</h5>
            {% else %}
            <h5 class="mb-0">Modifica Dettaglio Garanzia</h5>
            {% endif %}
        </div>

        <div class="card-body">
            {% set postUrl = routes('BE_WARRANTYDETAILS_SAVE') %}
            {% if curWarrantyDetails.id is not empty %}                
            {% set postUrl = routes('BE_WARRANTYDETAILS_SAVE') + '?warrantyDetailsId=' + curWarrantyDetails.id %}
            {% endif %}

            <form id="warrantydetails-edit" class="form-validate" method="POST" action="{{ postUrl }}" enctype="multipart/form-data">
                
                <div class="row mb-3">
                    <label class="col-lg-3 col-form-label">Garanzia: <span class="text-danger">*</span></label>
                    <div class="col-lg-9">
                        <select id="warrantySelect" name="warrantyId" class="form-control" data-placeholder="Seleziona garanzia" required {{ warrantyId is not empty ? 'disabled' : '' }}>
                            <option value=""></option>
                            {% set warranties = lookup('Warranty', checkPublished=false, language='false') %}
                            {% if warranties is not empty %}
                                {% for warranty in warranties %}
                                    <option value="{{ warranty.id }}" {{ curWarrantyDetails.warrantyId equals warranty.id or warrantyId equals warranty.id ? 'selected' : '' }}>{{ warranty.title }} ({{ warranty.code }})</option>
                                {% endfor %}
                            {% endif %}
                        </select>
                        <div class="form-text text-muted">Seleziona la garanzia di riferimento.</div>
                    </div>
                </div>

                <div class="row mb-3" data-field="provinceCode">
                    <label class="col-lg-3 col-form-label">Codici Provincia:</label>
                    <div class="col-lg-9">
                        <select name="provinceCode" class="form-control select" data-placeholder="Seleziona province" multiple>
                            {% set provinces = lookup('Province', checkPublished=false, language='false') %}
                            {% if provinces is not empty %}
                                {% for province in provinces %}
                                    <option value="{{ province.code }}" {{ curWarrantyDetails.provinceCode is not empty and curWarrantyDetails.provinceCode contains province.code ? 'selected' : '' }}>{{ province.code }} - {{ province.description }}</option>
                                {% endfor %}
                            {% endif %}
                        </select>
                        <div class="form-text text-muted">Seleziona i codici delle province di riferimento.</div>
                    </div>
                </div>

                <div class="row mb-3" data-field="claimNumber">
                    <label class="col-lg-3 col-form-label">Range Numero Sinistri:</label>
                    <div class="col-lg-9">
                        <div class="row">
                            <div class="col-md-6">
                                <input name="claimNumberFrom" type="number" class="form-control" placeholder="Da" value="{{ curWarrantyDetails.claimNumberMin }}" min="0">
                                <div class="form-text text-muted">Numero minimo di sinistri</div>
                            </div>
                            <div class="col-md-6">
                                <input name="claimNumberTo" type="number" class="form-control" placeholder="A" value="{{ curWarrantyDetails.claimNumberMax }}" min="0">
                                <div class="form-text text-muted">Numero massimo di sinistri</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mb-3" data-field="insuranceProvenanceTypeId">
                    <label class="col-lg-3 col-form-label">Tipo Provenienza Assicurativa:</label>
                    <div class="col-lg-9">
                        <select name="insuranceProvenanceTypeId" class="form-control" data-placeholder="Seleziona tipo provenienza">
                            <option value=""></option>
                            {% set insuranceProvenanceTypes = lookup('InsuranceProvenanceType', checkPublished=false, language='false') %}
                            {% if insuranceProvenanceTypes is not empty %}
                                {% for insuranceProvenanceType in insuranceProvenanceTypes %}
                                    <option value="{{ insuranceProvenanceType.id }}" {{ curWarrantyDetails.insuranceProvenanceTypeId equals insuranceProvenanceType.id ? 'selected' : '' }}>{{ insuranceProvenanceType.title }}</option>
                                {% endfor %}
                            {% endif %}
                        </select>
                        <div class="form-text text-muted">Seleziona il tipo di provenienza assicurativa.</div>
                    </div>
                </div>

                <div class="row mb-3" data-field="universalClass">
                    <label class="col-lg-3 col-form-label">Range Classe Universale:</label>
                    <div class="col-lg-9">
                        <div class="row">
                            <div class="col-md-6">
                                <input name="universalClassFrom" type="number" class="form-control" placeholder="Da" value="{{ curWarrantyDetails.universalClassMin }}" min="1" max="18">
                                <div class="form-text text-muted">Classe minima (da 1 a 18)</div>
                            </div>
                            <div class="col-md-6">
                                <input name="universalClassTo" type="number" class="form-control" placeholder="A" value="{{ curWarrantyDetails.universalClassMax }}" min="1" max="18">
                                <div class="form-text text-muted">Classe massima (da 1 a 18)</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mb-3" data-field="premiumValue">
                    <label class="col-lg-3 col-form-label">Valore Premio:</label>
                    <div class="col-lg-9">
                        <input name="premiumValue" type="number" step="0.01" class="form-control" placeholder="Valore del premio" value="{{ curWarrantyDetails.premiumValue }}" min="0">
                        <div class="form-text text-muted">Valore del premio assicurativo in euro.</div>
                    </div>
                </div>

                <div class="text-end">
                    <a href="{{ routes('BE_WARRANTYDETAILS_COLLECTION') }}" class="btn btn-light">
                        <i class="ph-arrow-left me-2"></i>
                        Torna alla lista
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="ph-check me-2"></i>
                        {% if curWarrantyDetails is empty %}
                        Crea Dettaglio
                        {% else %}
                        Aggiorna Dettaglio
                        {% endif %}
                    </button>
                </div>
            </form>
        </div>
    </div>
    <!-- /form -->
</div>
<!-- /content area -->

{% endblock %}
