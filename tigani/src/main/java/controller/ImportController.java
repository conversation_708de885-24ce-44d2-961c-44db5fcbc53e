package controller;

import core.Core;
import core.Pages;
import core.Routes;
import dao.BaseDao;
import dao.DaoFilters;
import dao.DaoFiltersOperation;
import enums.LogType;
import enums.ProfileType;
import org.apache.commons.lang3.StringUtils;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pojo.Brand;
import pojo.City;
import pojo.Country;
import pojo.ItalianaApiResponse;
import pojo.ItalianaApiItem;
import pojo.Model;
import pojo.QueryOptions;
import pojo.User;
import spark.Request;
import spark.Response;
import spark.Route;
import spark.TemplateViewRoute;
import utils.Defaults;
import utils.ExcelUtils;
import utils.HttpUtils;
import utils.RequestUtils;
import utils.UploadedFile;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * Controller for importing data from Excel files
 * 
 * <AUTHOR>
 */
public class ImportController {

    private static final Logger LOGGER = LoggerFactory.getLogger(ImportController.class.getName());

    public static TemplateViewRoute be_import = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);

        return Core.render(Pages.BE_IMPORT, attributes, request);
    };

    public static Route be_import_process = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);

        Map<String, String> params = new LinkedHashMap<>();
        Map<String, UploadedFile> files = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, files);

        String importType = params.get("importType");
        
        if (StringUtils.isBlank(importType)) {
            response.status(400);
            return "{ \"success\": false, \"message\": \"Tipo di importazione non specificato\" }";
        }

        // Check if files are required for this import type
        boolean requiresFile = !StringUtils.equalsIgnoreCase(importType, "marche") &&
                              !StringUtils.equalsIgnoreCase(importType, "modelli");

        if (requiresFile && files.isEmpty()) {
            response.status(400);
            return "{ \"success\": false, \"message\": \"Nessun file caricato\" }";
        }

        UploadedFile uploadedFile = null;
        if (requiresFile) {
            uploadedFile = files.values().iterator().next();

            // Validate file type
            if (!isValidExcelFile(uploadedFile)) {
                response.status(400);
                return "{ \"success\": false, \"message\": \"Il file deve essere un Excel (.xlsx)\" }";
            }
        }

        try {
            int importedCount = 0;
            int errorCount = 0;
            
            if (StringUtils.equalsIgnoreCase(importType, "comuni")) {
                importedCount = importCities(uploadedFile, user);
            } else if (StringUtils.equalsIgnoreCase(importType, "stati")) {
                importedCount = importCountries(uploadedFile, user);
            } else if (StringUtils.equalsIgnoreCase(importType, "marche")) {
                importedCount = importBrands(user);
            } else if (StringUtils.equalsIgnoreCase(importType, "modelli")) {
                importedCount = importModels(user);
            } else {
                response.status(400);
                return "{ \"success\": false, \"message\": \"Tipo di importazione non valido\" }";
            }

            response.type("application/json");
            return "{ \"success\": true, \"message\": \"Importazione completata con successo\", \"imported\": " + importedCount + " }";

        } catch (Exception ex) {
            LOGGER.error("Error during import process", ex);
            response.status(500);
            return "{ \"success\": false, \"message\": \"Errore durante l'importazione: " + ex.getMessage() + "\" }";
        }
    };

    private static boolean isValidExcelFile(UploadedFile file) {
        if (file == null || file.getContentType() == null) {
            return false;
        }
        
        String contentType = file.getContentType().toLowerCase();
        String fileName = file.getName() != null ? file.getName().toLowerCase() : "";
        
        return contentType.contains("spreadsheet") || 
               contentType.contains("excel") ||
               fileName.endsWith(".xlsx") ||
               fileName.endsWith(".xls");
    }

    private static int importCities(UploadedFile file, User user) {
        List<City> cities = ExcelUtils.parseCitiesExcel(file.getContent());
        int importedCount = 0;

        for (City city : cities) {
            try {
                // Check if city already exists by codice_istat
                if (StringUtils.isNotBlank(city.getCodiceIstat())) {
                    List<Bson> filters = new ArrayList<>();
                    filters.add(DaoFilters.getFilter("codiceIstat", DaoFiltersOperation.EQ, city.getCodiceIstat()));

                    QueryOptions queryOptions = DaoFilters.createQueryWithOptions(filters, 0, 1, null, null);
                    List<City> existingCities = BaseDao.getDocumentsByFilters(City.class, queryOptions);

                    if (existingCities.isEmpty()) {
                        // Insert new city
                        ObjectId cityId = BaseDao.insertDocument(city);
                        city.setId(cityId);
                        BaseDao.insertLog(user, city, LogType.INSERT);
                        importedCount++;
                        LOGGER.debug("Imported city: " + city.getName());
                    } else {
                        // Update existing city
                        City existingCity = existingCities.get(0);
                        existingCity.setName(city.getName());
                        existingCity.setCodiceBelfiore(city.getCodiceBelfiore());
                        existingCity.setCap(city.getCap());
                        existingCity.setProvinceCode(city.getProvinceCode());
                        existingCity.setProvince(city.getProvince());
                        existingCity.setRegion(city.getRegion());
                        existingCity.setCountryCode(city.getCountryCode());

                        BaseDao.updateDocument(existingCity);
                        BaseDao.insertLog(user, existingCity, LogType.UPDATE);
                        importedCount++;
                        LOGGER.debug("Updated city: " + city.getName());
                    }
                }
            } catch (Exception ex) {
                LOGGER.error("Error importing city: " + city.getName(), ex);
            }
        }

        return importedCount;
    }

    private static int importCountries(UploadedFile file, User user) {
        List<Country> countries = ExcelUtils.parseCountriesExcel(file.getContent());
        int importedCount = 0;

        for (Country country : countries) {
            try {
                // Check if country already exists by code
                if (StringUtils.isNotBlank(country.getCode())) {
                    List<Bson> filters = new ArrayList<>();
                    filters.add(DaoFilters.getFilter("code", DaoFiltersOperation.EQ, country.getCode()));

                    QueryOptions queryOptions = DaoFilters.createQueryWithOptions(filters, 0, 1, null, null);
                    List<Country> existingCountries = BaseDao.getDocumentsByFilters(Country.class, queryOptions);

                    if (existingCountries.isEmpty()) {
                        // Insert new country
                        ObjectId countryId = BaseDao.insertDocument(country);
                        country.setId(countryId);
                        BaseDao.insertLog(user, country, LogType.INSERT);
                        importedCount++;
                        LOGGER.debug("Imported country: " + country.getDescription());
                    } else {
                        // Update existing country
                        Country existingCountry = existingCountries.get(0);
                        existingCountry.setDescription(country.getDescription());
                        existingCountry.setCodiceBelfiore(country.getCodiceBelfiore());

                        BaseDao.updateDocument(existingCountry);
                        BaseDao.insertLog(user, existingCountry, LogType.UPDATE);
                        importedCount++;
                        LOGGER.debug("Updated country: " + country.getDescription());
                    }
                }
            } catch (Exception ex) {
                LOGGER.error("Error importing country: " + country.getDescription(), ex);
            }
        }

        return importedCount;
    }

    /**
     * Import brands from Italiana API
     *
     * @param user The user performing the import
     * @return Number of brands imported
     */
    private static int importBrands(User user) {
        int importedCount = 0;

        try {
            // Prepare API parameters
            Map<String, String> queryParams = new HashMap<>();
            queryParams.put("canale", Defaults.ITALIANA_CHANNEL);
            queryParams.put("codiceCompagnia", Defaults.ITALIANA_COMPANY_CODE);
            queryParams.put("codiceAgenzia", Defaults.ITALIANA_AGENCY_CODE);
            queryParams.put("codiceAgente", Defaults.ITALIANA_AGENT_CODE);
            queryParams.put("classeVeicolo", Defaults.ITALIANA_VEHICLE_CLASS);

            // Prepare headers
            Map<String, String> headers = new HashMap<>();
            headers.put("x-Gateway-APIKey", Defaults.ITALIANA_PRODUCTION_API_KEY);

            // Build API URL
            String apiUrl = Defaults.ITALIANA_PRODUCTION_GATEWAY + "/" + Defaults.ITALIANA_BRANDS_API;

            LOGGER.info("Calling brands API: {}", apiUrl);

            // Make API call
            String response = HttpUtils.get(apiUrl, queryParams, headers);
            LOGGER.debug("Brands API response: {}", response);

            // Parse response
            ItalianaApiResponse apiResponse = HttpUtils.parseJson(response, ItalianaApiResponse.class);

            if (!apiResponse.isValid()) {
                String errorMsg = "Invalid API response: " + apiResponse.getErrorMessage();
                LOGGER.error(errorMsg);
                throw new RuntimeException(errorMsg);
            }

            // Process brands
            for (ItalianaApiItem item : apiResponse.getPayload()) {
                try {
                    // Check if brand already exists by codice
                    if (StringUtils.isNotBlank(item.getCodice())) {
                        List<Bson> filters = new ArrayList<>();
                        filters.add(DaoFilters.getFilter("codice", DaoFiltersOperation.EQ, item.getCodice()));

                        QueryOptions queryOptions = DaoFilters.createQueryWithOptions(filters, 0, 1, null, null);
                        List<Brand> existingBrands = BaseDao.getDocumentsByFilters(Brand.class, queryOptions);

                        if (existingBrands.isEmpty()) {
                            // Insert new brand
                            Brand brand = new Brand();
                            brand.setCodice(item.getCodice());
                            brand.setDescrizione(item.getDescrizione());

                            ObjectId brandId = BaseDao.insertDocument(brand);
                            brand.setId(brandId);
                            BaseDao.insertLog(user, brand, LogType.INSERT);
                            importedCount++;
                            LOGGER.debug("Imported brand: {} - {}", item.getCodice(), item.getDescrizione());
                        } else {
                            // Update existing brand
                            Brand existingBrand = existingBrands.get(0);
                            existingBrand.setDescrizione(item.getDescrizione());

                            BaseDao.updateDocument(existingBrand);
                            BaseDao.insertLog(user, existingBrand, LogType.UPDATE);
                            importedCount++;
                            LOGGER.debug("Updated brand: {} - {}", item.getCodice(), item.getDescrizione());
                        }
                    }
                } catch (Exception ex) {
                    LOGGER.error("Error importing brand: {} - {}", item.getCodice(), item.getDescrizione(), ex);
                }
            }

            LOGGER.info("Successfully imported {} brands", importedCount);

        } catch (Exception ex) {
            LOGGER.error("Error during brands import", ex);
            throw new RuntimeException("Error during brands import: " + ex.getMessage(), ex);
        }

        return importedCount;
    }

    /**
     * Import models from Italiana API for all brands
     *
     * @param user The user performing the import
     * @return Number of models imported
     */
    private static int importModels(User user) {
        int importedCount = 0;

        try {
            // Get all brands from database
            List<Brand> brands = BaseDao.getDocumentsByClass(Brand.class);

            if (brands.isEmpty()) {
                LOGGER.warn("No brands found in database. Import brands first.");
                return 0;
            }

            // Get today's date in dd/MM/yyyy format
            SimpleDateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy");
            String today = dateFormat.format(new Date());

            LOGGER.info("Starting models import for {} brands", brands.size());

            // Process each brand
            for (Brand brand : brands) {
                try {
                    importedCount += importModelsForBrand(brand, today, user);
                } catch (Exception ex) {
                    LOGGER.error("Error importing models for brand: {} - {}", brand.getCodice(), brand.getDescrizione(), ex);
                }
            }

            LOGGER.info("Successfully imported {} models total", importedCount);

        } catch (Exception ex) {
            LOGGER.error("Error during models import", ex);
            throw new RuntimeException("Error during models import: " + ex.getMessage(), ex);
        }

        return importedCount;
    }

    /**
     * Import models for a specific brand
     *
     * @param brand The brand to import models for
     * @param registrationDate The registration date in dd/MM/yyyy format
     * @param user The user performing the import
     * @return Number of models imported for this brand
     */
    private static int importModelsForBrand(Brand brand, String registrationDate, User user) throws Exception {
        int importedCount = 0;

        // Prepare API parameters
        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("canale", Defaults.ITALIANA_CHANNEL);
        queryParams.put("codiceCompagnia", Defaults.ITALIANA_COMPANY_CODE);
        queryParams.put("codiceAgenzia", Defaults.ITALIANA_AGENCY_CODE);
        queryParams.put("codiceAgente", Defaults.ITALIANA_AGENT_CODE);
        queryParams.put("classeVeicolo", Defaults.ITALIANA_VEHICLE_CLASS);
        queryParams.put("codiceMarca", brand.getDescrizione()); // Use brand description as codiceMarca
        queryParams.put("dataImmatricolazione", registrationDate);

        // Prepare headers
        Map<String, String> headers = new HashMap<>();
        headers.put("x-Gateway-APIKeyx-Gateway-APIKey", Defaults.ITALIANA_PRODUCTION_API_KEY);

        // Build API URL
        String apiUrl = Defaults.ITALIANA_PRODUCTION_GATEWAY + "/" + Defaults.ITALIANA_MODELS_API;

        LOGGER.debug("Calling models API for brand {}: {}", brand.getDescrizione(), apiUrl);

        // Make API call
        String response = HttpUtils.get(apiUrl, queryParams, headers);
        LOGGER.debug("Models API response for brand {}: {}", brand.getDescrizione(), response);

        // Parse response
        ItalianaApiResponse apiResponse = HttpUtils.parseJson(response, ItalianaApiResponse.class);

        if (!apiResponse.isValid()) {
            String errorMsg = "Invalid API response for brand " + brand.getDescrizione() + ": " + apiResponse.getErrorMessage();
            LOGGER.warn(errorMsg);
            return 0; // Don't throw exception, just skip this brand
        }

        // Process models
        for (ItalianaApiItem item : apiResponse.getPayload()) {
            try {
                // Check if model already exists by codice and brandId
                if (StringUtils.isNotBlank(item.getCodice())) {
                    List<Bson> filters = new ArrayList<>();
                    filters.add(DaoFilters.getFilter("codice", DaoFiltersOperation.EQ, item.getCodice()));
                    filters.add(DaoFilters.getFilter("brandId", DaoFiltersOperation.EQ, brand.getId()));

                    QueryOptions queryOptions = DaoFilters.createQueryWithOptions(filters, 0, 1, null, null);
                    List<Model> existingModels = BaseDao.getDocumentsByFilters(Model.class, queryOptions);

                    if (existingModels.isEmpty()) {
                        // Insert new model
                        Model model = new Model();
                        model.setCodice(item.getCodice());
                        model.setDescrizione(item.getDescrizione());
                        model.setBrandId(brand.getId());

                        ObjectId modelId = BaseDao.insertDocument(model);
                        model.setId(modelId);
                        BaseDao.insertLog(user, model, LogType.INSERT);
                        importedCount++;
                        LOGGER.debug("Imported model: {} - {} for brand {}", item.getCodice(), item.getDescrizione(), brand.getDescrizione());
                    } else {
                        // Update existing model
                        Model existingModel = existingModels.get(0);
                        existingModel.setDescrizione(item.getDescrizione());

                        BaseDao.updateDocument(existingModel);
                        BaseDao.insertLog(user, existingModel, LogType.UPDATE);
                        importedCount++;
                        LOGGER.debug("Updated model: {} - {} for brand {}", item.getCodice(), item.getDescrizione(), brand.getDescrizione());
                    }
                }
            } catch (Exception ex) {
                LOGGER.error("Error importing model: {} - {} for brand {}", item.getCodice(), item.getDescrizione(), brand.getDescrizione(), ex);
            }
        }

        LOGGER.debug("Imported {} models for brand {}", importedCount, brand.getDescrizione());
        return importedCount;
    }
}
