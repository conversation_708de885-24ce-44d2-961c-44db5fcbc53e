package controller;

import core.Core;
import core.Pages;
import core.Routes;
import dao.BaseDao;
import dao.DaoFilters;
import dao.DaoFiltersOperation;
import enums.LogType;
import enums.ProfileType;
import org.apache.commons.lang3.StringUtils;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pojo.City;
import pojo.Country;
import pojo.QueryOptions;
import pojo.User;
import spark.Request;
import spark.Response;
import spark.Route;
import spark.TemplateViewRoute;
import utils.ExcelUtils;
import utils.RequestUtils;
import utils.UploadedFile;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * Controller for importing data from Excel files
 * 
 * <AUTHOR>
 */
public class ImportController {

    private static final Logger LOGGER = LoggerFactory.getLogger(ImportController.class.getName());

    public static TemplateViewRoute be_import = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);

        return Core.render(Pages.BE_IMPORT, attributes, request);
    };

    public static Route be_import_process = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);

        Map<String, String> params = new LinkedHashMap<>();
        Map<String, UploadedFile> files = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, files);

        String importType = params.get("importType");
        
        if (StringUtils.isBlank(importType)) {
            response.status(400);
            return "{ \"success\": false, \"message\": \"Tipo di importazione non specificato\" }";
        }

        if (files.isEmpty()) {
            response.status(400);
            return "{ \"success\": false, \"message\": \"Nessun file caricato\" }";
        }

        UploadedFile uploadedFile = files.values().iterator().next();
        
        // Validate file type
        if (!isValidExcelFile(uploadedFile)) {
            response.status(400);
            return "{ \"success\": false, \"message\": \"Il file deve essere un Excel (.xlsx)\" }";
        }

        try {
            int importedCount = 0;
            int errorCount = 0;
            
            if (StringUtils.equalsIgnoreCase(importType, "comuni")) {
                importedCount = importCities(uploadedFile, user);
            } else if (StringUtils.equalsIgnoreCase(importType, "stati")) {
                importedCount = importCountries(uploadedFile, user);
            } else {
                response.status(400);
                return "{ \"success\": false, \"message\": \"Tipo di importazione non valido\" }";
            }

            response.type("application/json");
            return "{ \"success\": true, \"message\": \"Importazione completata con successo\", \"imported\": " + importedCount + " }";

        } catch (Exception ex) {
            LOGGER.error("Error during import process", ex);
            response.status(500);
            return "{ \"success\": false, \"message\": \"Errore durante l'importazione: " + ex.getMessage() + "\" }";
        }
    };

    private static boolean isValidExcelFile(UploadedFile file) {
        if (file == null || file.getContentType() == null) {
            return false;
        }
        
        String contentType = file.getContentType().toLowerCase();
        String fileName = file.getName() != null ? file.getName().toLowerCase() : "";
        
        return contentType.contains("spreadsheet") || 
               contentType.contains("excel") ||
               fileName.endsWith(".xlsx") ||
               fileName.endsWith(".xls");
    }

    private static int importCities(UploadedFile file, User user) {
        List<City> cities = ExcelUtils.parseCitiesExcel(file.getContent());
        int importedCount = 0;

        for (City city : cities) {
            try {
                // Check if city already exists by codice_istat
                if (StringUtils.isNotBlank(city.getCodiceIstat())) {
                    List<Bson> filters = new ArrayList<>();
                    filters.add(DaoFilters.getFilter("codiceIstat", DaoFiltersOperation.EQ, city.getCodiceIstat()));

                    QueryOptions queryOptions = DaoFilters.createQueryWithOptions(filters, 0, 1, null, null);
                    List<City> existingCities = BaseDao.getDocumentsByFilters(City.class, queryOptions);

                    if (existingCities.isEmpty()) {
                        // Insert new city
                        ObjectId cityId = BaseDao.insertDocument(city);
                        city.setId(cityId);
                        BaseDao.insertLog(user, city, LogType.INSERT);
                        importedCount++;
                        LOGGER.debug("Imported city: " + city.getName());
                    } else {
                        // Update existing city
                        City existingCity = existingCities.get(0);
                        existingCity.setName(city.getName());
                        existingCity.setCodiceBelfiore(city.getCodiceBelfiore());
                        existingCity.setCap(city.getCap());
                        existingCity.setProvinceCode(city.getProvinceCode());
                        existingCity.setProvince(city.getProvince());
                        existingCity.setRegion(city.getRegion());
                        existingCity.setCountryCode(city.getCountryCode());

                        BaseDao.updateDocument(existingCity);
                        BaseDao.insertLog(user, existingCity, LogType.UPDATE);
                        importedCount++;
                        LOGGER.debug("Updated city: " + city.getName());
                    }
                }
            } catch (Exception ex) {
                LOGGER.error("Error importing city: " + city.getName(), ex);
            }
        }

        return importedCount;
    }

    private static int importCountries(UploadedFile file, User user) {
        List<Country> countries = ExcelUtils.parseCountriesExcel(file.getContent());
        int importedCount = 0;

        for (Country country : countries) {
            try {
                // Check if country already exists by code
                if (StringUtils.isNotBlank(country.getCode())) {
                    List<Bson> filters = new ArrayList<>();
                    filters.add(DaoFilters.getFilter("code", DaoFiltersOperation.EQ, country.getCode()));

                    QueryOptions queryOptions = DaoFilters.createQueryWithOptions(filters, 0, 1, null, null);
                    List<Country> existingCountries = BaseDao.getDocumentsByFilters(Country.class, queryOptions);

                    if (existingCountries.isEmpty()) {
                        // Insert new country
                        ObjectId countryId = BaseDao.insertDocument(country);
                        country.setId(countryId);
                        BaseDao.insertLog(user, country, LogType.INSERT);
                        importedCount++;
                        LOGGER.debug("Imported country: " + country.getDescription());
                    } else {
                        // Update existing country
                        Country existingCountry = existingCountries.get(0);
                        existingCountry.setDescription(country.getDescription());
                        existingCountry.setCodiceBelfiore(country.getCodiceBelfiore());

                        BaseDao.updateDocument(existingCountry);
                        BaseDao.insertLog(user, existingCountry, LogType.UPDATE);
                        importedCount++;
                        LOGGER.debug("Updated country: " + country.getDescription());
                    }
                }
            } catch (Exception ex) {
                LOGGER.error("Error importing country: " + country.getDescription(), ex);
            }
        }

        return importedCount;
    }
}
