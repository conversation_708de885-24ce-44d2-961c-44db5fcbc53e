package pojo;

import org.bson.types.ObjectId;

/**
 * POJO to store vehicle model data (fields aligned with API: codice, descrizione)
 */
public class Model extends BasePojo {

    private String codice;
    private String descrizione;
    private ObjectId brandId;  // Reference to the Brand entity

    public String getCodice() {
        return codice;
    }

    public void setCodice(String codice) {
        this.codice = codice;
    }

    public String getDescrizione() {
        return descrizione;
    }

    public void setDescrizione(String descrizione) {
        this.descrizione = descrizione;
    }

    public ObjectId getBrandId() {
        return brandId;
    }

    public void setBrandId(ObjectId brandId) {
        this.brandId = brandId;
    }
}

