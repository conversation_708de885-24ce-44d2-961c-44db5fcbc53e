package pojo;

/**
 * POJO to represent individual items in the Italiana API response payload
 * Contains codice and descrizione fields as returned by the API
 */
public class ItalianaApiItem {

    private String codice;
    private String descrizione;

    public String getCodice() {
        return codice;
    }

    public void setCodice(String codice) {
        this.codice = codice;
    }

    public String getDescrizione() {
        return descrizione;
    }

    public void setDescrizione(String descrizione) {
        this.descrizione = descrizione;
    }

    @Override
    public String toString() {
        return "ItalianaApiItem{" +
                "codice='" + codice + '\'' +
                ", descrizione='" + descrizione + '\'' +
                '}';
    }
}
