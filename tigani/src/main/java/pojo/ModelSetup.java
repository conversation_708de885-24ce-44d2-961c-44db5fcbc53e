package pojo;

import org.bson.types.ObjectId;

/**
 * POJO to store vehicle model setup/configuration data (allestimenti)
 * Fields aligned with API: codice, descrizione
 * Includes relationships to Brand and Model entities
 */
public class ModelSetup extends BasePojo {

    private String codice;
    private String descrizione;
    private Integer annoImmatricolazione, meseImmatricolazione;
    private Double valoreAssicurato;
    private ObjectId brandId;  // Reference to the Brand entity
    private ObjectId modelId;  // Reference to the Model entity

    public String getCodice() {
        return codice;
    }

    public void setCodice(String codice) {
        this.codice = codice;
    }

    public String getDescrizione() {
        return descrizione;
    }

    public void setDescrizione(String descrizione) {
        this.descrizione = descrizione;
    }

    public Integer getAnnoImmatricolazione() {
        return annoImmatricolazione;
    }

    public void setAnnoImmatricolazione(Integer annoImmatricolazione) {
        this.annoImmatricolazione = annoImmatricolazione;
    }

    public Integer getMeseImmatricolazione() {
        return meseImmatricolazione;
    }

    public void setMeseImmatricolazione(Integer meseImmatricolazione) {
        this.meseImmatricolazione = meseImmatricolazione;
    }

    public Double getValoreAssicurato() {
        return valoreAssicurato;
    }

    public void setValoreAssicurato(Double valoreAssicurato) {
        this.valoreAssicurato = valoreAssicurato;
    }

    public ObjectId getBrandId() {
        return brandId;
    }

    public void setBrandId(ObjectId brandId) {
        this.brandId = brandId;
    }

    public ObjectId getModelId() {
        return modelId;
    }

    public void setModelId(ObjectId modelId) {
        this.modelId = modelId;
    }

    @Override
    public String toString() {
        return "ModelSetup{" +
                "codice='" + codice + '\'' +
                ", descrizione='" + descrizione + '\'' +
                ", brandId=" + brandId +
                ", modelId=" + modelId +
                '}';
    }
}
